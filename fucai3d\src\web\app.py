# P10-Web界面系统 FastAPI主应用
# 为福彩3D预测闭环系统提供现代化Web界面

from fastapi import FastAPI, WebSocket, WebSocketDisconnect
from fastapi.responses import JSONResponse, HTMLResponse
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
import asyncio
from contextlib import asynccontextmanager
from datetime import datetime
import os
import sys
import time
import logging

# 配置日志
logger = logging.getLogger(__name__)

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from api_adapter import P9SystemAdapter
from websocket_manager import WebSocketManager
from routes.prediction import router as prediction_router
from routes.monitoring import router as monitoring_router
from routes.optimization import router as optimization_router
from routes.cache import router as cache_router
from cache_manager import cache_manager, periodic_cleanup

# 创建FastAPI应用
app = FastAPI(
    title="福彩3D智能预测系统",
    description="基于P9闭环自动优化的Web界面",
    version="1.0.0",
    docs_url="/api/docs",
    redoc_url="/api/redoc"
)

# 配置CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000"],  # React开发服务器
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册路由
app.include_router(prediction_router)
app.include_router(monitoring_router)
app.include_router(optimization_router)
app.include_router(cache_router)

# 初始化组件
api_adapter = None
websocket_manager = None

@app.on_event("startup")
async def startup_event():
    """应用启动事件"""
    global api_adapter, websocket_manager
    
    # 初始化P9系统适配器
    # 使用绝对路径确保数据库连接正确
    import os
    current_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.join(current_dir, "..", "..")
    db_path = os.path.join(project_root, "data", "fucai3d.db")
    api_adapter = P9SystemAdapter(db_path)
    
    # 初始化WebSocket管理器
    websocket_manager = WebSocketManager(api_adapter)
    
    # 启动后台任务
    asyncio.create_task(websocket_manager.start_background_tasks())

    # 启动缓存预热
    await cache_manager.warm_up_cache()

    # 启动定期清理任务
    asyncio.create_task(periodic_cleanup())

    print("🚀 福彩3D Web界面系统启动完成")
    print(f"📊 API文档: http://127.0.0.1:8000/api/docs")
    print(f"🔗 WebSocket: ws://127.0.0.1:8000/ws")

@app.get("/", response_class=HTMLResponse)
async def root():
    """根路径 - 返回简单的欢迎页面"""
    return """
    <!DOCTYPE html>
    <html>
    <head>
        <title>福彩3D智能预测系统</title>
        <meta charset="utf-8">
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
            .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            h1 { color: #1890ff; text-align: center; }
            .status { background: #f6ffed; border: 1px solid #b7eb8f; padding: 15px; border-radius: 5px; margin: 20px 0; }
            .links { display: flex; gap: 20px; justify-content: center; margin-top: 30px; }
            .links a { background: #1890ff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; }
            .links a:hover { background: #40a9ff; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🎯 福彩3D智能预测系统</h1>
            <div class="status">
                <h3>✅ 系统状态：运行正常</h3>
                <p><strong>技术栈：</strong>FastAPI + React + TypeScript</p>
                <p><strong>端口：</strong>8000 (避免与Flask API冲突)</p>
                <p><strong>P9集成：</strong>闭环优化系统已连接</p>
            </div>
            <div class="links">
                <a href="/api/docs" target="_blank">📚 API文档</a>
                <a href="/api/health" target="_blank">🔍 健康检查</a>
                <a href="/api/status" target="_blank">📊 系统状态</a>
            </div>
        </div>
    </body>
    </html>
    """

@app.get("/api/health")
async def health_check():
    """健康检查端点"""
    try:
        # 检查P9系统连接
        if api_adapter:
            system_health = await api_adapter._check_system_health()
            return {
                "status": "healthy",
                "timestamp": datetime.now().isoformat(),
                "p9_system": system_health,
                "websocket_connections": len(websocket_manager.connections) if websocket_manager else 0
            }
        else:
            return {
                "status": "initializing",
                "timestamp": datetime.now().isoformat(),
                "message": "系统正在初始化"
            }
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={
                "status": "error",
                "timestamp": datetime.now().isoformat(),
                "error": str(e)
            }
        )

@app.get("/api/status")
async def system_status():
    """系统状态端点"""
    try:
        if api_adapter:
            dashboard_data = await api_adapter.get_dashboard_data()
            return {
                "status": "success",
                "data": dashboard_data
            }
        else:
            return {
                "status": "error",
                "message": "P9系统适配器未初始化"
            }
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={
                "status": "error",
                "message": str(e)
            }
        )

# WebSocket端点
@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket连接端点"""
    if websocket_manager:
        await websocket_manager.connect(websocket)
        try:
            while True:
                # 使用超时机制接收客户端消息，避免无限等待
                try:
                    data = await asyncio.wait_for(websocket.receive_text(), timeout=30.0)
                    # 处理客户端发送的消息
                    if data == "ping":
                        await websocket.send_text("pong")
                    elif data == "heartbeat":
                        await websocket.send_text("heartbeat_ack")
                except asyncio.TimeoutError:
                    # 发送心跳保持连接活跃
                    await websocket.send_text("heartbeat")
                except Exception as e:
                    logger.error(f"WebSocket消息处理错误: {e}")
                    break
        except WebSocketDisconnect:
            websocket_manager.disconnect(websocket)
        except Exception as e:
            logger.error(f"WebSocket连接错误: {e}")
            websocket_manager.disconnect(websocket)
    else:
        await websocket.close(code=1000, reason="WebSocket管理器未初始化")

# 健康检查端点
startup_time = time.time()

@app.get("/health")
async def health_check():
    """健康检查端点"""
    try:
        # 检查数据库连接
        db_status = "healthy"
        if not os.path.exists("data/fucai3d.db"):
            db_status = "missing"

        # 检查缓存状态
        cache_stats = cache_manager.get_cache_stats()
        cache_status = "healthy" if cache_stats["size"] >= 0 else "error"

        # 检查WebSocket管理器
        ws_status = "healthy"  # 简化检查

        # 整体健康状态
        overall_status = "healthy"
        if db_status != "healthy" or cache_status != "healthy" or ws_status != "healthy":
            overall_status = "degraded"

        return {
            "status": overall_status,
            "timestamp": datetime.now().isoformat(),
            "version": "1.0.0",
            "components": {
                "database": db_status,
                "cache": cache_status,
                "websocket": ws_status
            },
            "uptime": time.time() - startup_time,
            "cache_stats": cache_stats
        }
    except Exception as e:
        return {
            "status": "error",
            "timestamp": datetime.now().isoformat(),
            "error": str(e)
        }

@app.get("/ready")
async def readiness_check():
    """就绪检查端点"""
    try:
        # 检查关键组件是否就绪
        checks = {
            "api": True,
            "cache": cache_manager.get_cache_stats()["size"] >= 0,
            "websocket": True  # 简化检查
        }

        all_ready = all(checks.values())

        return {
            "ready": all_ready,
            "timestamp": datetime.now().isoformat(),
            "checks": checks
        }
    except Exception as e:
        return {
            "ready": False,
            "timestamp": datetime.now().isoformat(),
            "error": str(e)
        }

if __name__ == "__main__":
    # 开发模式启动
    uvicorn.run(
        "app:app",
        host="127.0.0.1",
        port=8000,
        reload=True,
        log_level="info"
    )
